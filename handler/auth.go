package handler

import (
	"auth-session/global"
	"auth-session/logging"
	"database/sql"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"math/rand"
	"net/http"
	"strings"
	"time"

	"auth-session/server"

	"github.com/google/uuid"
)

// Signup signup
func Signup(db *sql.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		log.Println("signup")
		// read body
		body, err := ioutil.ReadAll(r.Body)
		if err != nil {
			server.ErrorResponse(w, http.StatusBadRequest, err.Error())
			return
		}
		var req signupRequest
		err = json.Unmarshal(body, &req)
		if err != nil {
			server.ErrorResponse(w, http.StatusBadRequest, err.Error())
			return
		}

		// TODO: データのバリデーション

		// password encrypt
		hash, err := server.PasswordHash(req.Password)
		log.Println(hash)
		if err != nil {
			server.ErrorResponse(w, http.StatusBadRequest, err.Error())
			return
		}

		// insert data
		_, err = db.Exec(
			"INSERT INTO users(username, name, password) VALUES (?,?,?)",
			req.Username,
			req.Name,
			hash,
		)

		// response
		server.Success(w, &signupResponse{
			Username: req.Username,
			Name:     req.Name,
			Password: hash,
		})
	}
}

type signupRequest struct {
	Username string `json:"username"`
	Name     string `json:"name"`
	Password string `json:"password"`
}

type signupResponse struct {
	Username string `json:"username"`
	Name     string `json:"name"`
	Password string `json:"password"`
}

// Login login
func Login(db *sql.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		log.Println("login")
		// read body
		body, err := ioutil.ReadAll(r.Body)
		if err != nil {
			server.ErrorResponse(w, http.StatusBadRequest, err.Error())
			return
		}
		var req loginRequest
		err = json.Unmarshal(body, &req)
		if err != nil {
			server.ErrorResponse(w, http.StatusBadRequest, err.Error())
			return
		}

		if len(req.Username) > 32 {
			server.ErrorResponse(w, http.StatusBadRequest, "用户名长度大于7")
			return
		}

		fmt.Println(req.Username, req.Password, req.Ukey)

		var rolename string
		row2 := db.QueryRow("SELECT role_name FROM users WHERE status='on' and username=?;", req.Username)

		if err = row2.Scan(&rolename); err != nil {
			s := fmt.Sprintf(";;;;%s;登录;;;失败", req.Username)
			logging.LogCustomMessage(logging.CustomLogRecord{Username: req.Username,
				Result:      "失败",
				Role:        "",
				Description: fmt.Sprintf("%s 登录失败", req.Username),
			})
			log.Println(s)
			server.ErrorResponse(w, http.StatusBadRequest, err.Error())
			return
		}

		// 检查ukey状态和值
		var ukeyStatus sql.NullString
		var ukeyWrite sql.NullString
		ukeyRow := db.QueryRow("SELECT ukey_status, ukey_write FROM users WHERE status='on' and username=?;", req.Username)

		if err = ukeyRow.Scan(&ukeyStatus, &ukeyWrite); err != nil {
			s := fmt.Sprintf(";;;;%s;登录;;;失败", req.Username)
			logging.LogCustomMessage(logging.CustomLogRecord{Username: req.Username,
				Result:      "失败",
				Role:        rolename,
				Description: fmt.Sprintf("%s 登录失败", req.Username),
			})
			log.Println(s)
			server.ErrorResponse(w, http.StatusBadRequest, "用户ukey信息查询失败")
			return
		}

		// 如果ukey_status为on，则启用ukey验证
		if ukeyStatus.Valid && ukeyStatus.String == "on" {
			// 检查请求中的ukey是否与数据库中的ukey_write一致
			if !ukeyWrite.Valid || req.Ukey != ukeyWrite.String {
				s := fmt.Sprintf(";;;;%s;登录;;;失败", req.Username)
				logging.LogCustomMessage(logging.CustomLogRecord{Username: req.Username,
					Result:      "失败",
					Role:        rolename,
					Description: fmt.Sprintf("%s ukey信息错误", req.Username),
				})
				log.Println(s)
				server.ErrorResponse(w, http.StatusBadRequest, "ukey信息错误")
				return
			}
		}

		var hash string
		var isFirstLogin sql.NullString

		if rolename == "operator" {
			row := db.QueryRow("SELECT password,is_first_login FROM users WHERE status='on' and username=? and (expiredday IS NULL OR expiredday > CURRENT_TIMESTAMP);", req.Username)
			if err = row.Scan(&hash, &isFirstLogin); err != nil {
				if err == sql.ErrNoRows {
					// 检查用户是否存在
					var userCount int
					err = db.QueryRow("SELECT COUNT(*) FROM users WHERE username=? AND status='on'", req.Username).Scan(&userCount)
					if err != nil {
						s := fmt.Sprintf(";;;;%s;登录;;;失败", req.Username)
						logging.LogCustomMessage(logging.CustomLogRecord{Username: req.Username,
							Result:      "失败",
							Role:        rolename,
							Description: fmt.Sprintf("%s 登录失败: %s", req.Username, err.Error()),
						})
						log.Println(s)
						server.ErrorResponse(w, http.StatusBadRequest, "系统错误")
						return
					}

					if userCount > 0 {
						// 用户存在但已过期
						s := fmt.Sprintf(";;;;%s;登录;;;失败", req.Username)
						logging.LogCustomMessage(logging.CustomLogRecord{Username: req.Username,
							Result:      "失败",
							Role:        rolename,
							Description: fmt.Sprintf("%s 登录失败，账户已过期", req.Username),
						})
						log.Println(s)
						server.ErrorResponse(w, http.StatusBadRequest, "账户已过期")
						return
					} else {
						// 用户不存在
						s := fmt.Sprintf(";;;;%s;登录;;;失败", req.Username)
						logging.LogCustomMessage(logging.CustomLogRecord{Username: req.Username,
							Result:      "失败",
							Role:        rolename,
							Description: fmt.Sprintf("%s 登录失败，用户不存在", req.Username),
						})
						log.Println(s)
						server.ErrorResponse(w, http.StatusBadRequest, "用户不存在")
						return
					}
				} else {
					s := fmt.Sprintf(";;;;%s;登录;;;失败", req.Username)
					logging.LogCustomMessage(logging.CustomLogRecord{Username: req.Username,
						Result:      "失败",
						Role:        rolename,
						Description: fmt.Sprintf("%s 登录失败: %s", req.Username, err.Error()),
					})
					log.Println(s)
					server.ErrorResponse(w, http.StatusBadRequest, err.Error())
					return
				}
			}
			err = server.PasswordVerify(hash, req.Password)
			if err != nil {
				// 处理登录失败，记录失败次数
				if handleErr := handleLoginFailure(db, req.Username); handleErr != nil {
					log.Printf("处理登录失败记录时出错: %v", handleErr)
				}

				s := fmt.Sprintf(";;;;%s;登录;;;失败", req.Username)
				logging.LogCustomMessage(logging.CustomLogRecord{Username: req.Username,
					Result:      "失败",
					Role:        rolename,
					Description: fmt.Sprintf("%s 登录失败", req.Username),
				})
				log.Println(s)
				fmt.Println("密码错误：", err.Error())
				server.ErrorResponse(w, 400, "密码错误")
				return
			}

			// operator 首次登录强制修改密码
			if rolename == "operator" && isFirstLogin.Valid && isFirstLogin.String == "y" {
				server.Success(w, &msgResponse{Msg: "first"})
				return
			}

		} else {
			row := db.QueryRow("SELECT password FROM users WHERE status='on'  and username=?", req.Username)
			if err = row.Scan(&hash); err != nil {
				s := fmt.Sprintf(";;;;%s;登录;;;失败", req.Username)
				logging.LogCustomMessage(logging.CustomLogRecord{Username: req.Username,
					Result:      "失败",
					Role:        rolename,
					Description: fmt.Sprintf("%s 登录失败", req.Username),
				})
				log.Println(s)
				server.ErrorResponse(w, http.StatusBadRequest, err.Error())
				return
			}
			err = server.PasswordVerify(hash, req.Password)
			if err != nil {
				// 处理登录失败，记录失败次数
				if handleErr := handleLoginFailure(db, req.Username); handleErr != nil {
					log.Printf("处理登录失败记录时出错: %v", handleErr)
				}

				s := fmt.Sprintf(";;;;%s;登录;;;失败", req.Username)
				logging.LogCustomMessage(logging.CustomLogRecord{Username: req.Username,
					Result:      "失败",
					Role:        rolename,
					Description: fmt.Sprintf("%s 登录失败", req.Username),
				})
				log.Println(s)
				fmt.Println("密码错误：", err.Error())
				server.ErrorResponse(w, 400, "密码错误")
				return
			}

		}

		//获取用户数据进行筛选
		var userid string
		info := db.QueryRow("SELECT id FROM users WHERE status='on' and username=?;", req.Username)
		if err = info.Scan(&userid); err != nil {
			// server.ErrorResponse(w, http.StatusBadRequest, err.Error())
			fmt.Println("用户user_id获取失败", err)
		} else {
			if err = LoginPasswordJudge(db, userid, req.Username); err != nil {
				result := strings.Split(err.Error(), ",")
				server.NewErrorResponseToken(w, 200, result[0], result[1])
				return
			}
		}

		if global.CONFIG.License.Enabled {
			// 设置请求 URL 和方法
			url := fmt.Sprintf("%s", global.CONFIG.License.VerifyUrl)

			// 创建一个 GET 请求
			req1, err := http.NewRequest("GET", url, nil)
			if err != nil {
				server.NewErrorResponse(w, http.StatusBadRequest, err.Error())
				return
			}

			// 发送请求
			client := &http.Client{}
			resp, err := client.Do(req1)
			if err != nil {
				fmt.Println("发送请求失败:", err)
			}
			defer resp.Body.Close()

			// 检查响应状态码
			if resp.StatusCode != http.StatusOK {
				server.NewErrorResponse(w, http.StatusBadRequest, fmt.Sprintf("请检测授权服务是否正常，状态码：%d\n"))
				return
			}
			// 读取响应体
			body1, err := ioutil.ReadAll(resp.Body)
			if err != nil {
				server.NewErrorResponse(w, http.StatusBadRequest, fmt.Sprintf("请检测授权服务是否正常"))
				return
			}
			// 定义响应结构体
			type GResponse struct {
				Code int         `json:"code"`
				Msg  string      `json:"msg"`
				Data interface{} `json:"data"`
			}

			// 解析 JSON 响应
			var response GResponse
			err = json.Unmarshal(body1, &response)
			if err != nil {
				server.NewErrorResponse(w, http.StatusBadRequest, fmt.Sprintf("请检测授权服务是否正常"))
				return
			}
			if response.Code == 200 {
				fmt.Println("授权通过")
			} else {
				//// 将 Data 转换为 string
				//dataStr, ok := response.Data.(string)
				//if !ok {
				//	server.ErrorResponse(w, http.StatusBadRequest, fmt.Sprintf("请检测授权服务是否正常"))
				//	return
				//}
				server.NewErrorResponse(w, http.StatusOK, fmt.Sprintf("nolicense"))
				return
			}
		}

		// create session id
		sessionID, err := uuid.NewRandom()
		if err != nil {
			server.ErrorResponse(w, http.StatusBadRequest, err.Error())
			return
		}

		// update session
		_, err = db.Exec(
			"UPDATE users SET session_id = ? WHERE username=?",
			sessionID.String(),
			req.Username,
		)
		if err != nil {

			server.ErrorResponse(w, http.StatusBadRequest, err.Error())
			return
		}

		// create cookie
		cookie := http.Cookie{
			Name:  "sessionid",
			Value: sessionID.String(),
			Path:  "/",
		}
		http.SetCookie(w, &cookie)
		// create cookie
		cookie_user := http.Cookie{
			Name:    "username",
			Value:   req.Username,
			Path:    "/",
			Expires: time.Now().Add(30 * time.Minute),
		}
		http.SetCookie(w, &cookie_user)

		// create cookie
		cookierole := http.Cookie{
			Name:  "role",
			Value: rolename,
			Path:  "/",
		}
		http.SetCookie(w, &cookierole)

		// 登录成功，清除失败记录
		if clearErr := clearLoginFailure(db, req.Username); clearErr != nil {
			log.Printf("清除登录失败记录时出错: %v", clearErr)
		}

		s := fmt.Sprintf(";;;;%s;登录;;%s;成功", req.Username, rolename)
		logging.LogCustomMessage(logging.CustomLogRecord{Username: req.Username,
			Result:      "成功",
			Role:        rolename,
			Description: fmt.Sprintf("%s 登录成功", req.Username),
		})
		log.Println(s)

		// response
		server.Success(w, &msgResponse{Msg: "ok"})

	}
}

func LoginPasswordJudge(db *sql.DB, userid string, username string) error {
	query := "SELECT pwd_expired_day,status FROM password_expired_config LIMIT 1"
	var data PasswordExpiredConfig
	err := db.QueryRow(query).Scan(&data.PwdExpiredDay, &data.Status)
	if err != nil {
		// fmt.Println("登录密码策略验证报错，<<<<<", err)
		return nil
	}

	if data.Status == "off" { //未开启密码策略验证，无需验证，直接返回
		return nil
	}

	var createstr string //用户修改密码记录时间
	recordquery := "SELECT status,create_at FROM password_updated_record WHERE user_id = ? ORDER BY create_at DESC  LIMIT 1"
	var record PasswordUpdatedRecord
	err = db.QueryRow(recordquery, userid).Scan(&record.Status, &createstr)
	if err == sql.ErrNoRows {
		fmt.Println("第一次登录，向记录表插入数据")
		record = PasswordUpdatedRecord{
			UserId:   userid,
			Status:   "on",
			CreateAt: time.Now(),
		}
		insertQuery := "INSERT INTO password_updated_record (user_id,status,create_at) VALUES (?, ?, ?)"
		_, err := db.Exec(insertQuery, &record.UserId, &record.Status, &record.CreateAt)
		if err != nil {
			fmt.Println("数据库插入数据出错", err)
			return nil
		}
		createstr = time.Now().Format(time.DateTime)

	} else if err != nil {
		fmt.Println("用户密码记录修改表查询出错，<<<", err)
		return nil
	}

	if record.Status == "off" { //用户未开启密码策略验证，无需验证，直接返回
		return nil
	}

	// 解析时间字符串
	parsedTime, err := time.Parse(time.DateTime, createstr)
	if err != nil {
		fmt.Println("时间解析错误:", err)
		return nil
	}
	// 添加90天
	resultTime := parsedTime.Add(time.Duration(data.PwdExpiredDay) * 24 * time.Hour)
	if !time.Now().Before(resultTime) {
		fmt.Println("密码过期，修改密码")
		//向密码过期表插入token
		token := generateRandomString(10)
		insertQuery := "INSERT INTO password_expired_token (username,token,state,created_at,updated_at) VALUES (?, ?, ?, ?, ?)"
		_, err := db.Exec(insertQuery, username, token, "on", time.Now(), time.Now())
		if err != nil {
			fmt.Println("数据表token 插入数据出错", err)
		} else {
			fmt.Println("token信息插入完成")
		}
		return fmt.Errorf("expire,%s", token)
	}

	return nil
}

type loginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
	Ukey     string `json:"ukey"`
}

// LoginFailureRecord 登录失败记录结构体
type LoginFailureRecord struct {
	ID           int       `json:"id"`
	Username     string    `json:"username"`
	FailureCount int       `json:"failure_count"`
	IsAlert      bool      `json:"is_alert"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// Logout logout
func Logout(db *sql.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		log.Println("logout")

		// 需要清除的cookie名称列表
		cookiesToClear := []string{
			//"role",
			//"login_region",
			//"login_domain",
			//"csrftoken",
			//"sessionid",
			"username",
		}

		// 清除每个cookie
		for _, name := range cookiesToClear {
			cookie := &http.Cookie{
				Name:   name,
				Value:  "",
				Path:   "/",
				MaxAge: -1,
				//HttpOnly: true,
				//Secure:   true, // 如果使用HTTPS，保持为true
				SameSite: http.SameSiteStrictMode,
			}
			http.SetCookie(w, cookie)
		}

		server.Success(w, &msgResponse{Msg: "ok"})
	}
}

type messageResponse struct {
	Message string `json:"message"`
}

type msgResponse struct {
	Msg string `json:"msg"`
}

func generateRandomString(length int) string {
	rand.NewSource(time.Now().UnixNano())

	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)

	for i := range result {
		result[i] = charset[rand.Intn(len(charset))]
	}

	return string(result)
}

// handleLoginFailure 处理登录失败，记录失败次数并检查是否需要告警
func handleLoginFailure(db *sql.DB, username string) error {
	// 首先确保表存在
	createTableQuery := `
	CREATE TABLE IF NOT EXISTS login_failure_records (
		id INT PRIMARY KEY AUTO_INCREMENT,
		username VARCHAR(32) NOT NULL COMMENT '用户名',
		failure_count INT NOT NULL DEFAULT 0 COMMENT '连续失败次数',
		is_alert BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否告警',
		created_at DATETIME NOT NULL COMMENT '创建时间',
		updated_at DATETIME NOT NULL COMMENT '更新时间',
		UNIQUE KEY unique_username (username)
	)`
	_, err := db.Exec(createTableQuery)
	if err != nil {
		log.Printf("创建登录失败记录表失败: %v", err)
		// 继续执行，可能表已存在
	}

	// 使用事务确保数据一致性
	tx, err := db.Begin()
	if err != nil {
		log.Printf("开始事务失败: %v", err)
		return err
	}
	defer tx.Rollback()

	// 查询当前用户的失败记录
	var record LoginFailureRecord
	query := "SELECT id, username, failure_count, is_alert, created_at, updated_at FROM login_failure_records WHERE username = ? FOR UPDATE"
	err = tx.QueryRow(query, username).Scan(&record.ID, &record.Username, &record.FailureCount, &record.IsAlert, &record.CreatedAt, &record.UpdatedAt)

	now := time.Now()

	if err == sql.ErrNoRows {
		// 没有记录，创建新记录
		insertQuery := "INSERT INTO login_failure_records (username, failure_count, is_alert, created_at, updated_at) VALUES (?, ?, ?, ?, ?)"
		_, err = tx.Exec(insertQuery, username, 1, false, now, now)
		if err != nil {
			log.Printf("创建登录失败记录失败: %v", err)
			return err
		}
		log.Printf("用户 %s 首次登录失败，记录失败次数: 1", username)
	} else if err != nil {
		log.Printf("查询登录失败记录失败: %v", err)
		return err
	} else {
		// 增加失败次数
		newFailureCount := record.FailureCount + 1
		isAlert := newFailureCount >= 3

		// 更新记录
		updateQuery := "UPDATE login_failure_records SET failure_count = ?, is_alert = ?, updated_at = ? WHERE username = ?"
		result, err := tx.Exec(updateQuery, newFailureCount, isAlert, now, username)
		if err != nil {
			log.Printf("更新登录失败记录失败: %v", err)
			return err
		}

		rowsAffected, _ := result.RowsAffected()
		if rowsAffected == 0 {
			log.Printf("警告: 更新登录失败记录时没有影响任何行，用户: %s", username)
		}

		if isAlert && !record.IsAlert {
			log.Printf("用户 %s 连续登录失败 %d 次，触发告警", username, newFailureCount)
		} else {
			log.Printf("用户 %s 登录失败，当前失败次数: %d", username, newFailureCount)
		}
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		log.Printf("提交事务失败: %v", err)
		return err
	}

	return nil
}

// clearLoginFailure 清除登录失败记录
func clearLoginFailure(db *sql.DB, username string) error {
	// 删除该用户的失败记录
	deleteQuery := "DELETE FROM login_failure_records WHERE username = ?"
	result, err := db.Exec(deleteQuery, username)
	if err != nil {
		log.Printf("清除登录失败记录失败: %v", err)
		return err
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected > 0 {
		log.Printf("用户 %s 登录成功，已清除失败记录", username)
	} else {
		log.Printf("用户 %s 登录成功，但没有找到失败记录需要清除", username)
	}

	return nil
}

// getLoginFailureRecord 获取用户的登录失败记录（内部使用）
func getLoginFailureRecord(db *sql.DB, username string) (*LoginFailureRecord, error) {
	var record LoginFailureRecord
	query := "SELECT id, username, failure_count, is_alert, created_at, updated_at FROM login_failure_records WHERE username = ?"
	err := db.QueryRow(query, username).Scan(&record.ID, &record.Username, &record.FailureCount, &record.IsAlert, &record.CreatedAt, &record.UpdatedAt)

	if err == sql.ErrNoRows {
		return nil, nil // 没有记录
	} else if err != nil {
		return nil, err
	}

	return &record, nil
}
