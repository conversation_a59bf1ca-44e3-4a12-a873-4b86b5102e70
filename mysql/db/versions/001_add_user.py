# from sqlalchemy import *
# from migrate import *
#
#
# from sqlalchemy import Table, Column, Integer, String, MetaData
#
# meta = MetaData()
#
# account = Table(
#     'users', meta,
#     Column('id', Integer, primary_key=True),
#     Column('username', String(32)),
#     Column('name', String(32)),
#     Column('password', String(128)),
#     Column('session_id', String(128)),
# )
#
#
# def upgrade(migrate_engine):
#     meta.bind = migrate_engine
#     account.create()
#
#
# def downgrade(migrate_engine):
#     meta.bind = migrate_engine
#     account.drop()
#
