package main

import (
	"auth-session/global"
	"auth-session/handler"
	"auth-session/logging"
	"context"
	"crypto"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"database/sql"
	"encoding/base64"
	"encoding/pem"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/go-ini/ini"

	_ "github.com/go-sql-driver/mysql"
	_ "github.com/lib/pq"

	"github.com/canonical/go-dqlite/client"
	_ "github.com/canonical/go-dqlite/client"
	"github.com/canonical/go-dqlite/driver"
	_ "github.com/canonical/go-dqlite/driver"
)

func InitConfigFromIni() {
	err := ini.MapTo(global.CONFIG, "config.ini")
	//本地开发环境使用
	//err := ini.MapTo(global.CONFIG, "./config.ini")
	if err != nil {
		log.Println(err)
		return
	}
	fmt.Println(global.CONFIG.System.LDAP_SERVER)
	fmt.Println(global.CONFIG.System.BASE_DN)
	fmt.Println(global.CONFIG.System.DOMAIN)
	fmt.Println(global.CONFIG.System.USER)
	fmt.Println(global.CONFIG.System.PSW)
	fmt.Println(global.CONFIG.System.FILTER)
	fmt.Println(global.CONFIG.System.ETCD_HOST)

}

var (
	db               *sql.DB // 全局数据库连接
	driverRegistered bool    // 标记 dqlite 驱动是否已注册
	nodeAddrGlobal   string  // 保存全局连接地址
	dbNameGlobal     string  // 保存全局数据库名称
)

type logWriter struct {
	file *os.File
}

// 定义一个符合 client.LogFunc 签名的日志函数
func logFunc(level client.LogLevel, format string, a ...interface{}) {
	log.Printf("[%s] %s", level.String(), fmt.Sprintf(format, a...))
}

func (l *logWriter) openNew() error {
	name := global.CONFIG.Log.File

	mode := os.FileMode(0644)

	f, err := os.OpenFile(name, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, mode)
	if err != nil {
		return fmt.Errorf("can't open new logfile: %s", err)
	}

	l.file = f
	return nil
}

func (l *logWriter) openExistingOrNew(writeLen int) error {

	filename := global.CONFIG.Log.File
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		return l.openNew()
	} else if err != nil {
		return fmt.Errorf("error getting log file info: %s", err)
	}

	file, err := os.OpenFile(filename, os.O_APPEND|os.O_WRONLY, 0644)
	if err != nil {
		return l.openNew()
	}
	l.file = file
	return nil
}

func (writer logWriter) Write(bytes []byte) (n int, err error) {
	if writer.file == nil {
		if err = writer.openExistingOrNew(len(bytes)); err != nil {
			fmt.Printf("write fail, msg(%s)\n", err)
			return 0, err
		}
	}
	//var p = fmt.Sprintf(time.Now().UTC().Format("2006/01/02 15:04:05") + ";" + string(bytes))
	var p = fmt.Sprintf(time.Now().Format("2006/01/02 15:04:05") + string(bytes))

	n, err = writer.file.Write([]byte(p))

	return n, err

}

func InitLog() {
	logFile, err := os.OpenFile(global.CONFIG.Log.File, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)

	if err != nil {
		fmt.Println("open log file failed, err:", err)
		return
	}
	log.SetOutput(logFile)
	//log.SetFlags(log.Llongfile | log.Lmicroseconds | log.Ldate)
	log.SetFlags(0)
	log.SetOutput(new(logWriter))
}

func Verify(params string, pub *rsa.PublicKey, sign string) (err error) {
	// 和签名步骤相同，对收到的请求参数按照字母顺序进行排序

	// 和签名步骤相同，将排序后的signature进行hash操作
	h := sha256.New()
	h.Write([]byte(params))
	Sha256Code := h.Sum(nil)

	//Sha256Code := sha256.Sum256(params)

	// 对签名进行base64解码
	decodeSignature, err := base64.StdEncoding.DecodeString(sign)
	// 使用rsa验签函数
	// 第一个参数是公钥
	// 第二个参数是hash函数
	// 第三个参数是被hash函数处理过的原始输入
	// 第四个参数是被处理过的签名

	err = rsa.VerifyPKCS1v15(pub, crypto.SHA256, Sha256Code, decodeSignature)
	if err != nil { // 验证失败
		return err
	}
	return nil // 验证成功
}

func verfiytest() {
	pemEncodedPublicKey :=
		[]byte(`-----BEGIN RSA PUBLIC KEY-----
MIGJAoGBAJ4uJBgelEAibo/ElBrrSJcWeg93WJSbhzF/JfAJ2MLyom5fxHrwUB0D
sebKAvP9yqltqWTnlPT71GGi941cCKcZVAn6boQbo1IQ6uARvz1mRf8qqmsJ8F7b
vCQpjlyXkoNCio0dWUp76OmKdKNIHAJTq7BC91nqK5HIhfKA+I2bAgMBAAE=
-----END RSA PUBLIC KEY-----`)

	pkcs1RSAPublicKey, _ := pem.Decode(pemEncodedPublicKey)
	pubkey, err := x509.ParsePKCS1PublicKey(pkcs1RSAPublicKey.Bytes)
	if err != nil {
		log.Fatalf("Could not parse PKCS1 RSA public key: %s", err)
	}

	//s := "PRGPXJb2SgOziTJ6qaFswKVZGK0G+1CFVEwfDC5Bm9uJuoZyDnKjN52fL68oW0QX7Kuh2pYff11VcgedQTZuFn+1qZxjCSfxSKsW2uVX3aak6X9ipUgGBMpNpSIxHkzymmPznO9BsyQEy2h+If5lcrunCFP28hq4aT0a5CjHO7A="

	s := "dz3N8OdYrYrwxTkB1fZw3glcZytPwKArkXSEHZnTK4OQM3K6vutjw1hkFAjnwmwo2fU+w2h5i+YNu01oduoFwSxvPWKVgzelSUT4oTtylC93U9TkjkWVaHTVR9mNG9xg7SfkMKJm3uhLqupcf1yIQ56ryUuNJQJzKUII0iNt/uM="

	Verify(`{"cpu":20, "org":"华能XXXX", "service_start_date":"2022-07-01", "service_start_date":"2024-06-01"}`, pubkey, s)
}

func main() {

	//verfiytest()
	//return
	InitConfigFromIni()
	// global.SetupJsonLog()
	logging.JsonLogInit()
	InitLog()
	//log.Println(";;;;;;maojj;登录;成功")
	// connect db
	DB, err := initDB()
	if err != nil {
		log.Fatal(err)
	}

	// routing
	http.HandleFunc("/login", handler.Login(DB))
	http.HandleFunc("/logout", handler.Logout(DB))
	http.HandleFunc("/signup", handler.Signup(DB))
	http.HandleFunc("/license", handler.License(DB))

	http.HandleFunc("/sessions/whoami", handler.WhoAmI(DB))

	//全局配置设置
	http.HandleFunc("/v2/setconfig", handler.SetConfig(DB))
	//获取全局配置
	http.HandleFunc("/v2/getconfig", handler.GetConfig(DB))

	//修改密码
	http.HandleFunc("/v2/setpassword", handler.SetPassword(DB))

	//修改密码带token
	http.HandleFunc("/v2/setpassword/token", handler.SetPasswordByToken(DB))

	// start server11
	log.Println("server running...")
	http.ListenAndServe(":8088", nil)
}

func initDB() (*sql.DB, error) {
	if global.CONFIG.Db.Drive == "mysql" {

		//return sql.Open("mysql", "root:newroot@tcp(localhost:3306)/theauth")
		var dbUrl string
		switch global.CONFIG.Db.Drive {
		case "kingbase":
			dbUrl = fmt.Sprintf("user=%s password=%s host=%s port=%s dbname=%s sslmode=disable",
				global.CONFIG.Db.Username, global.CONFIG.Db.Password, global.CONFIG.Db.Host,
				global.CONFIG.Db.Port, global.CONFIG.Db.Database)
			fmt.Println(dbUrl)
			return sql.Open("postgres", dbUrl)
		case "mysql":
			dbUrl = fmt.Sprintf("%s:%s@tcp(%s:%s)/%s",
				global.CONFIG.Db.Username, global.CONFIG.Db.Password, global.CONFIG.Db.Host,
				global.CONFIG.Db.Port, global.CONFIG.Db.Database)
			fmt.Println(dbUrl)
			return sql.Open("mysql", dbUrl)
		}

	}

	if global.CONFIG.Db.Drive == "dqlite" {

		if !driverRegistered {
			//node := "************:9001"
			node := fmt.Sprintf("%s:%s", global.CONFIG.Db.Host, global.CONFIG.Db.Port)

			nodeInfo := []client.NodeInfo{
				{ID: 1, Address: node},
			}

			nodeStore := client.NewInmemNodeStore()
			if err := nodeStore.Set(context.Background(), nodeInfo); err != nil {
				log.Fatalf("Failed to set node info: %v", err)
				return nil, err
			}

			driver, err := driver.New(nodeStore, driver.WithLogFunc(logFunc))
			if err != nil {
				fmt.Println("Failed to create dqlite driver:", err)
				return nil, err
			}

			// 注册 dqlite 驱动，只执行一次
			sql.Register("dqlite", driver)
			driverRegistered = true // 标记驱动已注册
		}

		// 连接到 dqlite 数据库
		dbNameGlobal = global.CONFIG.Db.Database
		database := global.CONFIG.Db.Database

		var err error
		db, err = sql.Open("dqlite", database)
		if err != nil {
			fmt.Println("Failed to connect to dqlite:", err)
			return nil, err
		}

		db.SetMaxIdleConns(10)           // 设置空闲连接池大小
		db.SetMaxOpenConns(50)           // 设置最大连接数
		db.SetConnMaxLifetime(time.Hour) // 设置连接的最大生存时间为1小时

		return db, nil
	}

	return nil, fmt.Errorf("数据库连接失败，请查询数据库配置")
}
